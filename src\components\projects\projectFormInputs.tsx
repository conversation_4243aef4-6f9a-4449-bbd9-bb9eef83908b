import type { UseFormRegisterReturn } from "react-hook-form";
import { PROJECT_STATUS_OPTIONS } from "@/const";
import { useProjectContext } from "@/hooks/useProjectContext";

const formClasses = {
  label: "block text-nowrap text-sm font-bold text-gray-700",
  input:
    "block px-3 py-1 min-h-[32px] border font-normal text-gray-800 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
  ruler: "border-blue-300 border-[1px]  mb-2 w-full mt-3",
  tooltip:
    "absolute z-50 left-1/2 -translate-x-32 w-[140px] mt-2 hidden group-hover:block  px-2 py-1 text-sm text-white bg-gray-600 rounded whitespace-normal break-words",
};

export const ProjectLink: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  doesProjectExist: (link: string, type: "link" | "ref") => void;
}> = ({ labelClass, inputClass, doesProjectExist, register }) => {
  return (
    <label htmlFor="projectLink" className={labelClass}>
      Project Link
      <input
        type="text"
        id="projectLink"
        placeholder="Project Link"
        className={inputClass}
        {...register}
        data-testid="projectLinkInput"
        onChange={(e) => doesProjectExist(e.target.value, "link")}
      />
    </label>
  );
};

export const JobReference: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  doesProjectExist: (jobReference: string, type: "link" | "ref") => void;
}> = ({ labelClass, inputClass, doesProjectExist, register }) => (
  <label htmlFor="jobReference" className={labelClass}>
    Reference-ID
    <input
      type="text"
      id="jobReference"
      placeholder="Reference ID"
      maxLength={20}
      className={inputClass}
      {...register}
      data-testid="projectJobReferenceInput"
      onChange={(e) => doesProjectExist(e.target.value, "ref")}
    />
  </label>
);

export const ProjectStatus: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="status" className={labelClass}>
    Status
    <select id="status" className={inputClass} {...register} data-testid="statusInput">
      {PROJECT_STATUS_OPTIONS.map((option) => (
        <option key={option} value={option}>
          {option}
        </option>
      ))}
    </select>
  </label>
);

export const ProjectTitle: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  error?: string;
}> = ({ labelClass, inputClass, register, error }) => (
  <label htmlFor="projectTitle" className={`${labelClass} relative`}>
    Project Title<span className="text-red-500">*</span>
    <input
      type="text"
      id="projectTitle"
      placeholder="Project Title"
      maxLength={100}
      className={`${inputClass} ${error ? "border-red-500" : ""}`}
      {...register}
      data-testid="projectTitleInput"
    />
    {error === "Project Title is required" && <InputWarning message={error} fieldName="projectTitle" />}
  </label>
);

export const DateOfApplication: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  error?: string;
}> = ({ labelClass, inputClass, register, error }) => {
  return (
    <label htmlFor="dateOfApplication" className={`${labelClass} relative`}>
      Date of Application<span className="text-red-500">*</span>
      <input
        type="date"
        id="dateOfApplication"
        data-testid="dateOfApplicationInput"
        className={`${inputClass} ${error ? "border-red-500" : ""}`}
        {...register}
      />
      {error === "Date of Application is required" && <InputWarning message={error} align="right" fieldName="dateOfApplication" />}
    </label>
  );
};

export const DateOfProjectStart: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="dateOfProjectStart" className={labelClass}>
    Project Start
    <input type="date" id="dateOfProjectStart" data-testid="dateOfProjectStartInput" className={inputClass} {...register} />
  </label>
);

export const ProjectLocation: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="location" className={labelClass}>
    Location
    <input
      type="text"
      id="location"
      placeholder="Project Location"
      maxLength={50}
      className={inputClass}
      {...register}
      data-testid="projectLocationInput"
    />
  </label>
);

export const RemoteInput: React.FC<{
  labelClass: string;
  inputClass: string;
  isRemote: boolean;
  toggleIsRemote: () => void;
  register: UseFormRegisterReturn;
  error?: string;
}> = ({ isRemote, toggleIsRemote, labelClass, inputClass, register, error }) => (
  <>
    <div className="w-[120px]">
      <span className="flex justify-between">
        <label htmlFor="remote" className={labelClass}>
          <input type="checkbox" id="remote" checked={isRemote} onChange={toggleIsRemote} data-testid="remoteCheckbox" />
          Remote
        </label>
        <span className="relative group cursor-help">
          &#9432;
          <span className={formClasses.tooltip}>Check to enable the remote percentage input. When unchecked, remote work defaults to 0%</span>
        </span>
      </span>

      <div className="relative">
        <input
          className={`${inputClass} ${error ? "border-red-500" : ""}`}
          type="number"
          disabled={!isRemote}
          data-testid="remoteNumberInput"
          {...register}
        />
        {error === "Between 0-100 allowed" && <InputWarning message={error} fieldName="remote" />}
        <span className="absolute inset-y-0 right-2 flex items-center text-gray-500 text-sm">%</span>
      </div>
    </div>
  </>
);

export const UtilizationInput: React.FC<{
  labelClass: string;
  inputClass: string;
  isPartTime: boolean;
  register: UseFormRegisterReturn;
  error?: string;
  toggleIsPartTime: () => void;
}> = ({ isPartTime, toggleIsPartTime, labelClass, inputClass, register, error }) => (
  <div className="w-[120px]">
    <span className="flex justify-between">
      <label htmlFor="utilization" className={labelClass}>
        <input type="checkbox" id="utilization" checked={isPartTime} onChange={toggleIsPartTime} data-testid="utilizationCheckbox" />
        Part-time
      </label>
      <span className="relative group cursor-help">
        &#9432;
        <span className={formClasses.tooltip}>Check to enable the utilization percentage input. When unchecked, utilization defaults to 100%</span>
      </span>
    </span>

    <div className="relative">
      <input
        className={`${inputClass} ${error ? "border-red-500" : ""}`}
        type="number"
        {...register}
        disabled={!isPartTime}
        data-testid="utilizationInput"
      />
      {error === "Between 0-100 allowed" && <InputWarning message={error} align="right" fieldName="utilization" />}
      <span className="absolute inset-y-0 right-2 flex items-center text-gray-500 text-sm">%</span>
    </div>
  </div>
);

export const RateInput: React.FC<{
  labelClass: string;
  inputClass: string;
  registerOnsite: UseFormRegisterReturn;
  registerOffsite: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, registerOnsite, registerOffsite }) => (
  <label htmlFor="rateOnsite" className={labelClass}>
    Net Rate
    <span className="flex gap-2">
      <span>
        <input type="text" id="rateOnsite" maxLength={50} {...registerOnsite} placeholder="€" className={inputClass} data-testid="rateOnsiteInput" />
        <p className="text-xs text-gray-400">(Onsite)</p>
      </span>

      <span>
        <input
          type="text"
          id="rateOffsite"
          maxLength={50}
          {...registerOffsite}
          placeholder="€"
          className={inputClass}
          data-testid="rateOffsiteInput"
        />
        <p className="text-xs text-gray-400">(Offsite)</p>
      </span>
    </span>
  </label>
);

export const CreatedForInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  errors: {
    createdForUserId?: string;
    createdByUserId?: string;
  };
}> = ({ labelClass, inputClass, register, errors }) => {
  const { users } = useProjectContext();

  return (
    users?.length && (
      <label htmlFor="createdForUser" className={`${labelClass} /*relative*/`}>
        Created for<span className="text-red-500">*</span>
        <div className="relative">
          <select
            id="createdForUser"
            className={`${inputClass} ${errors.createdForUserId ? "border-red-500" : ""}`}
            {...register}
            data-testid="createdForUserInput"
          >
            <option value="">Select a user</option>
            {users.map((user) => (
              <option key={user.id} value={user.id}>
                {user.name}
              </option>
            ))}
          </select>
          {errors.createdForUserId && <InputWarning message={errors.createdForUserId} align="left" fieldName="createdForUser" />}
        </div>
      </label>
    )
  );
};

export const RoleInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="role" className={labelClass}>
    Role
    <input type="text" id="role" placeholder="Role" maxLength={100} className={inputClass} {...register} data-testid="roleInput" />
  </label>
);

export const PlatformInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="platform" className={labelClass}>
    Platform
    <input type="text" id="platform" maxLength={50} {...register} placeholder="where or how?" className={inputClass} data-testid="platformInput" />
  </label>
);

export const AgencyInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="agency" className={labelClass}>
    Agency Name
    <input type="text" id="agency" maxLength={50} {...register} placeholder="Agency Name" className={inputClass} data-testid="agencyInput" />
  </label>
);

export const PoC_Input: React.FC<{
  labelClass: string;
  inputClass: string;
  registerFirstName: UseFormRegisterReturn;
  registerLastName: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, registerFirstName, registerLastName }) => (
  <label htmlFor="contactFirstName" className={labelClass}>
    Contact Name
    <span className="flex gap-3">
      <span>
        <input
          type="text"
          id="contactFirstName"
          maxLength={50}
          {...registerFirstName}
          placeholder="First Name"
          className={inputClass}
          data-testid="contactFirstNameInput"
        />
        <p className="text-xs text-gray-400 mb-1">(First name)</p>
      </span>
      <span>
        <input
          type="text"
          id="contactLastName"
          maxLength={50}
          {...registerLastName}
          placeholder="Last Name"
          className={inputClass}
          data-testid="contactLastNameInput"
        />
        <p className="text-xs text-gray-400">(Last name)</p>
      </span>
    </span>
  </label>
);

export const EmailInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  error?: string;
}> = ({ labelClass, inputClass, register, error }) => (
  <label htmlFor="email" className={`${labelClass} relative`}>
    Contact Email
    <input id="email" type="text" maxLength={50} {...register} placeholder="PoC Email" className={inputClass} data-testid="emailInput" />
    {error === "Enter a valid email" && <InputWarning message={error} fieldName="email" />}
  </label>
);

export const PhoneInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
  error?: string;
}> = ({ labelClass, inputClass, register, error }) => (
  <label htmlFor="number" className={`${labelClass} relative`}>
    Contact Phone
    <input id="number" type="text" maxLength={20} {...register} placeholder="PoC Phone" className={inputClass} data-testid="phoneInput" />
    {error === "Enter a valid international phone number" && <InputWarning message={error} fieldName="phone" />}
  </label>
);

export const CommentInput: React.FC<{
  labelClass: string;
  inputClass: string;
  register: UseFormRegisterReturn;
}> = ({ labelClass, inputClass, register }) => (
  <label htmlFor="comment" className={labelClass}>
    Comment
    <textarea id="comment" maxLength={500} {...register} placeholder="Anything to say?" className={inputClass} data-testid="commentInput" />
  </label>
);

export const ActionButtons: React.FC<{
  classes: { wrapper: string; cancel: string; save: string };
  closeProjectForm: () => void;
}> = ({ closeProjectForm, classes }) => (
  <div className={classes.wrapper}>
    <button type="button" onClick={closeProjectForm} className={classes.cancel} data-testid="cancelProjectButton">
      Cancel
    </button>
    <button type="submit" className={classes.save} data-testid="saveProjectButton">
      Save
    </button>
  </div>
);

export const FormSection: React.FC<{
  children: React.ReactNode;
  classes?: string;
  noRuler?: boolean;
  title?: string;
}> = ({ children, classes, noRuler, title }) => (
  <>
    {(title || !noRuler) && (
      <span className="flex mb-3">
        {title && <h2 className="text-nowrap bold mr-1 text-blue-500">{title}</h2>}
        {!noRuler && <hr className={formClasses.ruler} />}
      </span>
    )}

    <section className={`flex flex-wrap gap-3 ${classes}`}>{children}</section>
  </>
);

export const InputWarning: React.FC<{
  message?: string;
  align?: "left" | "right" | "center";
  fieldName?: string;
}> = ({ message, align = "left", fieldName }) => {
  const tooltipPosition = {
    left: "left-0",
    right: "right-0",
    center: "left-1/2 -translate-x-1/2",
  }[align];

  const arrowPosition = {
    left: "left-3",
    right: "right-3",
  };

  return (
    <div
      className={`absolute top-full mt-2 w-max max-w-xs bg-yellow-100 font-normal 
        text-gray-800 text-sm px-2 py-1 rounded shadow-md border border-gray-400 z-10 
        ${tooltipPosition}`}
    >
      {align === "center" ? (
        <>
          <div
            className={`absolute -top-[6px] left-3 w-[10px] h-[10px] 
              bg-yellow-100 rotate-45 z-[-1] border-gray-400 border-l border-t`}
            style={{ boxSizing: "border-box" }}
          />
          <div
            className={`absolute -top-[6px] right-3 w-[10px] h-[10px] 
              bg-yellow-100 rotate-45 z-[-1] border-gray-400 border-l border-t`}
            style={{ boxSizing: "border-box" }}
          />
        </>
      ) : (
        <div
          className={`absolute -top-[6px] w-[10px] h-[10px] bg-yellow-100 rotate-45 z-[-1]
            border-gray-400 border-l border-t text-sm ${arrowPosition[align]}`}
          style={{ boxSizing: "border-box" }}
        />
      )}

      <div className="flex items-center gap-1" data-testid={`${fieldName}InputWarning`}>
        <span className="pb-0.5">⚠️</span>
        {message}
      </div>
    </div>
  );
};
