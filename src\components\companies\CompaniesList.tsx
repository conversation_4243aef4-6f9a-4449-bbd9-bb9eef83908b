"use client";

import { useState } from "react";
import { CompanyListItem, type Company } from "./CompanyListItem";
import { EmptyState } from "@/components/shared";

// Dummy company data
const dummyCompanies: Company[] = [
  {
    id: 1,
    name: "Tech Solutions Inc",
    industry: "Technology",
    size: "50-200 employees",
    location: "San Francisco, CA",
    website: "https://techsolutions.com",
    description: "Leading provider of enterprise software solutions",
    contactCount: 3,
    lastInteraction: "2024-01-15",
    status: "Active",
  },
  {
    id: 2,
    name: "Design Co",
    industry: "Design & Creative",
    size: "10-50 employees",
    location: "New York, NY",
    website: "https://designco.com",
    description: "Award-winning creative agency specializing in digital experiences",
    contactCount: 2,
    lastInteraction: "2024-01-10",
    status: "Active",
  },
  {
    id: 3,
    name: "Startup XYZ",
    industry: "Fintech",
    size: "10-50 employees",
    location: "Austin, TX",
    website: "https://startupxyz.com",
    description: "Innovative fintech startup revolutionizing digital payments",
    contactCount: 1,
    lastInteraction: "2024-01-08",
    status: "Prospect",
  },
  {
    id: 4,
    name: "Global Manufacturing Corp",
    industry: "Manufacturing",
    size: "1000+ employees",
    location: "Detroit, MI",
    website: "https://globalmanufacturing.com",
    description: "International manufacturing company with operations worldwide",
    contactCount: 5,
    lastInteraction: "2023-12-20",
    status: "Inactive",
  },
];

export const CompaniesList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCompanies = dummyCompanies.filter((company) => {
    return (
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.location.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  const handleAddCompany = () => {
    console.log("Add company clicked");
  };

  const handleEditCompany = (company: Company) => {
    console.log("Edit company:", company);
  };

  const handleViewCompany = (company: Company) => {
    console.log("View company:", company);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Companies</h2>
          <p className="text-gray-600">Manage your company relationships</p>
        </div>
        <button onClick={handleAddCompany} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
          Add Company
        </button>
      </div>

      {/* Filters */}
      <div className="flex-1 max-w-md">
        <input
          type="text"
          placeholder="Search companies..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Companies Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredCompanies.map((company) => (
          <CompanyListItem key={company.id} company={company} onEdit={handleEditCompany} onView={handleViewCompany} />
        ))}
      </div>

      {filteredCompanies.length === 0 && (
        <EmptyState
          icon={
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          }
          title="No companies found"
          description="Try adjusting your search terms or add a new company."
          action={{
            label: "Add Company",
            onClick: handleAddCompany,
          }}
        />
      )}
    </div>
  );
};
