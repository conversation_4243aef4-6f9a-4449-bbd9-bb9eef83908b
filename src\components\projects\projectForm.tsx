"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import type { ProjectFormData, Project } from "@/types";
import { formatDate, addProject, updateProject, requiredField, emailPattern, phonePattern, percentageRange } from "@/utils";
import {
  ProjectLink,
  JobReference,
  ProjectStatus,
  ProjectTitle,
  DateOfApplication,
  DateOfProjectStart,
  ProjectLocation,
  RemoteInput,
  UtilizationInput,
  RateInput,
  CreatedForInput,
  RoleInput,
  PlatformInput,
  AgencyInput,
  PoC_Input,
  EmailInput,
  PhoneInput,
  CommentInput,
  ActionButtons,
  FormSection,
} from "./projectFormInputs";
import { useProjectContext } from "@/hooks/useProjectContext";
import { InputWarning } from "@/components/projects/projectFormInputs";

const formClasses = {
  label: "block text-nowrap text-sm font-bold text-gray-700",
  input:
    "block px-3 py-1 min-h-[32px] border font-normal text-gray-800 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
  numberInput:
    "block px-3 py-1 w-full min-h-[32px] border font-normal border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",
  ruler: "border-gray-300 mb-2 mt-3",
  select:
    "block px-3 py-1 min-h-[32px] border font-normal text-gray-800 border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 sm:text-sm",
};

export const ProjectForm: React.FC<{
  project?: ProjectFormData | null;
  isActive: boolean;
  closeProjectForm: () => void;
}> = ({ project, isActive, closeProjectForm }) => {
  const { filteredData: projects, endpoint } = useProjectContext();

  const [loading, setLoading] = useState(false);

  const [isRemote, setIsRemote] = useState<boolean>(!project?.remote || Number(project.remote) > 0);
  const [isPartTime, setIsPartTime] = useState<boolean>(Number(project?.utilization) < 100 && Number(project?.utilization) >= 0);

  const [duplicateProjectLink, setDuplicateProjectLink] = useState("");
  const [duplicateProjectRef, setDuplicateProjectRef] = useState("");

  const {
    register,
    handleSubmit,
    watch,
    resetField,
    formState: { errors },
    clearErrors,
    reset,
  } = useForm<ProjectFormData>({
    mode: "onSubmit",
    defaultValues: {
      status: project?.status || "active",
      dateOfApplication: project?.dateOfApplication
        ? new Date(project?.dateOfApplication).toISOString().split("T")[0]
        : new Date().toISOString().split("T")[0],
      createdForUserId: project?.createdForUserId || "",
      platform: project?.platform || "",
      projectTitle: project?.projectTitle || "",
      location: project?.location || "",
      remote: project?.remote || "100",
      projectLink: project?.projectLink || "",
      agency: project?.agency || "",
      contactFirstName: project?.contactFirstName || "",
      contactLastName: project?.contactLastName || "",
      email: project?.email || "",
      number: project?.number || "",
      comment: project?.comment || "",
      jobReference: project?.jobReference || "",
      rateOnsite: project?.rateOnsite || "",
      rateOffsite: project?.rateOffsite || "",
      dateOfProjectStart: project?.dateOfProjectStart ? new Date(project?.dateOfProjectStart).toISOString().split("T")[0] : null,
      lastUpdated: project?.lastUpdated || new Date(),
      utilization: project?.utilization || "100",
      role: project?.role || "",
    },
  });

  const commentValue = watch("comment")?.length || 0;

  const onSubmit = async (data: any) => {
    if (!project?.id) {
      setLoading(true);
      const transformed = transformPayload(data);
      await addProject(transformed, endpoint);
      setLoading(false);
      closeForm();
      return;
    }

    const updatedFields = getUpdatedFields(project, data);
    if (!updatedFields) return closeProjectForm();

    setLoading(true);
    await updateProject(project.id, updatedFields, endpoint);
    setLoading(false);
    closeForm();
  };

  const toggleIsRemote = () => {
    setIsRemote(!isRemote);
    resetField("remote", { defaultValue: isRemote ? "0" : "100" });
  };

  const toggleIsPartTime = () => {
    setIsPartTime(!isPartTime);
    resetField("utilization", { defaultValue: !isPartTime ? "50" : "100" });
  };

  const doesProjectExist = (target: Project["projectLink"] | Project["jobReference"], type: "link" | "ref") => {
    if (type === "link") {
      const linkFound = projects.find((p) => p.projectLink === target || `${p.projectLink}/` === target);
      if (linkFound) setDuplicateProjectLink(linkFound?.projectLink!!);
      else setDuplicateProjectLink("");
      return;
    } else {
      const refFound = projects.find((p) => p.jobReference === target || `#${p.jobReference}` === target);
      if (refFound) setDuplicateProjectRef(refFound?.jobReference!!);
      else setDuplicateProjectRef("");
      return;
    }
  };

  if (!isActive) return null;

  const remoteError = errors.remote?.message;
  const utilizationError = errors.utilization?.message;
  const percentageErrors = remoteError && utilizationError;

  const closeForm = () => {
    setIsRemote(true);
    setIsPartTime(false);
    setDuplicateProjectLink("");
    setDuplicateProjectRef("");
    reset();
    clearErrors();
    closeProjectForm();
  };

  return (
    <div className="px-4 py-4 mx-5 my-2  font-normal shadow-xl rounded-lg bg-gray-50">
      <form onSubmit={handleSubmit(onSubmit)} className={`mb-3 ${loading ? "pointer-events-none opacity-50" : ""}`} data-testid="projectForm">
        <span className="flex items-center justify-between mb-3">
          <h2 className="bold text-xl text-gray-800">{!!project?.id ? `Edit ${project.projectTitle}` : "Add new Project"}</h2>

          {(!!duplicateProjectLink || !!duplicateProjectRef) && (
            <p
              className="bg-orange-600 px-2 text-white text.sm max-w-[500px] transition-all duration-300 ease-in-out text-xs"
              data-testid="duplicateProjectWarning"
            >
              Notice: A project with this Url or ReferenceID already exists. You can still proceed with adding this project, but please ensure this is
              not an unintended duplicate.
            </p>
          )}
        </span>

        <FormSection title="Project Details" classes="mb-3 items-end">
          <ProjectLink
            labelClass={formClasses.label + " flex-1"}
            inputClass={`${formClasses.input} w-full`}
            doesProjectExist={doesProjectExist}
            register={register("projectLink")}
          />

          <JobReference
            labelClass={formClasses.label}
            inputClass={formClasses.input}
            doesProjectExist={doesProjectExist}
            register={register("jobReference")}
          />

          <ProjectStatus
            labelClass="flex items-center size-fit gap-2 pl-2 text-gray-200 bg-blue-500 rounded-md [&_select]:bg-white [&_select]:text-gray-800"
            inputClass={formClasses.select + " rounded-l-none"}
            register={register("status")}
          />
        </FormSection>

        <FormSection classes="mb-3" noRuler>
          <ProjectTitle
            labelClass={formClasses.label + " flex-1"}
            inputClass={formClasses.input + " w-full"}
            register={register("projectTitle", requiredField("Project Title"))}
            error={errors.projectTitle?.message}
          />

          <DateOfApplication
            labelClass={formClasses.label}
            inputClass={formClasses.input}
            register={register("dateOfApplication", requiredField("Date of Application"))}
            error={errors.dateOfApplication?.message}
          />
        </FormSection>

        <FormSection noRuler classes="mb-3 justify-between">
          <span className="flex gap-3">
            <DateOfProjectStart labelClass={formClasses.label} inputClass={formClasses.input} register={register("dateOfProjectStart")} />

            <ProjectLocation labelClass={formClasses.label} inputClass={formClasses.input} register={register("location")} />

            <RoleInput labelClass={formClasses.label} inputClass={formClasses.input} register={register("role")} />
          </span>

          <span className="flex gap-3 relative">
            <RemoteInput
              labelClass={`${formClasses.label} flex items-center gap-1`}
              inputClass={`text-gray-${isRemote ? "800" : "400"} ${formClasses.numberInput}`}
              register={register("remote", percentageRange)}
              error={!percentageErrors ? remoteError : undefined}
              isRemote={isRemote}
              toggleIsRemote={toggleIsRemote}
            />
            <UtilizationInput
              labelClass={`${formClasses.label} flex items-center gap-1`}
              inputClass={`text-gray-${isPartTime ? "800" : "400"} ${formClasses.numberInput}`}
              register={register("utilization", percentageRange)}
              error={!percentageErrors ? utilizationError : undefined}
              isPartTime={isPartTime}
              toggleIsPartTime={toggleIsPartTime}
            />
            {percentageErrors === "Between 0-100 allowed" && <InputWarning message={percentageErrors} fieldName="percentage" align="center" />}
          </span>
        </FormSection>

        <FormSection title="Offer Details" classes="justify-between">
          <span className="flex gap-3">
            <CreatedForInput
              labelClass={formClasses.label}
              inputClass={formClasses.input}
              register={register("createdForUserId", requiredField("Created for"))}
              errors={{
                createdForUserId: errors.createdForUserId?.message,
              }}
            />

            <PlatformInput labelClass={formClasses.label} inputClass={formClasses.input} register={register("platform")} />
          </span>

          <RateInput
            labelClass={formClasses.label}
            inputClass={formClasses.input + " max-w-[80px]"}
            registerOnsite={register("rateOnsite")}
            registerOffsite={register("rateOffsite")}
          />
        </FormSection>

        <FormSection title="Contact Details">
          <span className="min-w-[300px]">
            <AgencyInput labelClass={formClasses.label + " mb-3"} inputClass={formClasses.input + " w-full"} register={register("agency")} />

            <PoC_Input
              labelClass={formClasses.label}
              inputClass={formClasses.input}
              registerFirstName={register("contactFirstName")}
              registerLastName={register("contactLastName")}
            />
          </span>

          <span className="min-w-[300px]">
            <EmailInput
              labelClass={formClasses.label + " mb-3"}
              inputClass={formClasses.input + " w-full"}
              register={register("email", emailPattern)}
              error={errors.email?.message}
            />

            <PhoneInput
              labelClass={formClasses.label}
              inputClass={formClasses.input + " w-full"}
              register={register("number", phonePattern)}
              error={errors.number?.message}
            />
          </span>

          <span className="grow">
            <CommentInput labelClass={formClasses.label + " w-full"} inputClass={formClasses.input + " w-full h-24"} register={register("comment")} />
            <p className={`text-sm text-gray-400 ${commentValue >= 500 ? "text-red-500" : ""}`}>{commentValue} / 500</p>
          </span>
        </FormSection>

        <ActionButtons
          classes={{
            wrapper: "flex gap-4 justify-end items-end",
            cancel: "bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400",
            save: "bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",
          }}
          closeProjectForm={closeForm}
        />
      </form>
    </div>
  );
};

function getUpdatedFields(project: any, formData: { [k: string]: FormDataEntryValue }) {
  const updatedFields: any = {};
  const sanitizedPayload = sanitizePayload(formData);

  Object.keys(sanitizedPayload).forEach((key) => {
    const newValue = sanitizedPayload[key];
    const oldValue = project[key];
    const isDate = key === "dateOfApplication" || key === "dateOfProjectStart";
    const newValueDate = isDate && newValue ? formatDate(newValue, "YYYYMMDD") : newValue;
    const oldValueDate = isDate && oldValue ? formatDate(oldValue, "YYYYMMDD") : oldValue;

    if (newValue === oldValue || (newValueDate === oldValueDate && isDate)) {
      return;
    }

    // assign Date object to dateOfApplication
    if (isDate && newValueDate !== oldValueDate) {
      return (updatedFields[key] = newValue ? new Date(newValue) : null);
    }

    updatedFields[key] = newValue;
  });

  return Object.keys(updatedFields).length > 0 ? updatedFields : null;
}

const sanitizePayload = (data: any) => {
  let newData = { ...data, remote: data.remote || "0", utilization: data.utilization || "100" };

  Object.keys(newData).forEach((key) => {
    if (!newData[key]) newData[key] = null;
  });

  return newData;
};

const transformPayload = (data: any) => {
  let newData = { ...data, remote: data.remote || "0", utilization: data.utilization || "100" };

  Object.keys(newData).forEach((key) => {
    if (!newData[key]) newData[key] = null;
    if (key === "dateOfApplication") newData[key] = new Date(newData[key]);
    if (key === "dateOfProjectStart" && !!newData[key]) newData[key] = new Date(newData[key]);
  });

  return newData;
};
