interface Company {
  id: number;
  name: string;
  industry: string;
  size: string;
  location: string;
  website: string;
  description: string;
  contactCount: number;
  lastInteraction: string;
  status: string;
}

interface CompanyListItemProps {
  company: Company;
  onEdit?: (company: Company) => void;
  onView?: (company: Company) => void;
}

export const CompanyListItem: React.FC<CompanyListItemProps> = ({ company, onEdit, onView }) => {
  const handleEdit = () => {
    onEdit?.(company);
  };

  const handleView = () => {
    onView?.(company);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-lg">{company.name}</h3>
            <p className="text-sm text-gray-600">{company.industry}</p>
          </div>
        </div>
        <span className="px-2 py-1 rounded-full text-xs font-medium">{company.status}</span>
      </div>

      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{company.description}</p>

      <div className="space-y-2 text-sm mb-4">
        <div className="flex items-center text-gray-600">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>{company.location}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
            />
          </svg>
          <span>{company.size}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
          </svg>
          <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 break-all">
            {company.website.replace("https://", "")}
          </a>
        </div>
      </div>

      <div className="flex justify-between items-center pt-4 border-t border-gray-100">
        <div className="text-sm text-gray-600">
          <span className="font-medium">{company.contactCount}</span> contacts
          <span className="mx-2">•</span>
          <span>Last: {company.lastInteraction}</span>
        </div>
        <div className="flex space-x-2">
          <button onClick={handleEdit} className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded text-sm transition-colors">
            Edit
          </button>
          <button onClick={handleView} className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm transition-colors">
            View
          </button>
        </div>
      </div>
    </div>
  );
};

export type { Company, CompanyListItemProps };
