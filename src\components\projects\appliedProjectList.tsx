"use client";

import { ProjectForm } from "./projectForm";
import { useProjectContext } from "@/hooks/useProjectContext";
import { AppliedProjectListItem } from "./appliedProjectListItem";
import { LoadingSpinner } from "../shared";

export const ProjectList: React.FC<{
  showToast: (message: string) => void;
}> = ({ showToast }) => {
  const { filteredData: projects, projectFormIsOpen, closeNewProjectForm, projectToDuplicate, openNewProjectForm, isLoading } = useProjectContext();

  if (isLoading) return <LoadingSpinner />;

  return (
    <div className="max-w-[1200px] mx-auto min-h-[70vh]" data-testid="projectsListContainer">
      {!!projectToDuplicate && <ProjectForm project={projectToDuplicate} closeProjectForm={closeNewProjectForm} isActive={projectFormIsOpen} />}

      {projects.length === 0 && !projectFormIsOpen && (
        <div className="flex flex-col items-center justify-center w-full h-full p-4 text-center">
          <h2 className="text-2xl font-bold text-gray-700">No projects found</h2>

          <p className="mt-2 text-gray-500">You can create a new project by clicking the button below.</p>

          <button onClick={() => openNewProjectForm()} className="mt-4 px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600">
            Create Project
          </button>
        </div>
      )}

      {projects.length > 0 && projects.map((project) => <AppliedProjectListItem key={project.id} project={project} showToast={showToast} />)}
    </div>
  );
};
