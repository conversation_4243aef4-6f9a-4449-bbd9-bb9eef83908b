import { NextResponse } from "next/server";

import { prisma } from "@@/prisma";
import { auth } from "@/auth";

export const GET = auth(async function GET() {
  try {
    const companies = await prisma.company.findMany({
      where: { isDeleted: false },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(companies, { status: 200 });
  } catch (error) {
    return NextResponse.json({
      message: `Error getting companies: ${error}`,
      status: 500,
    });
  }
});

export const POST = auth(async function POST(req) {
  try {
    const body = await req.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Company name is required" },
        { status: 400 }
      );
    }

    const newCompany = await prisma.company.create({
      data: {
        name: body.name,
        location: body.location || null,
        website: body.website || null,
        description: body.description || null,
      },
    });

    return NextResponse.json(newCompany, { status: 201 });
  } catch (error) {
    return NextResponse.json({
      message: `Error creating company: ${error}`,
      status: 500,
    });
  }
});
