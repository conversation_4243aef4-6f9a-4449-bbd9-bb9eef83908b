import "./globals.css";

import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { redirect } from "next/navigation";
import { SessionProvider } from "next-auth/react";

import { TME_navbar } from "@/components/shared";
import { auth } from "@/auth";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "ProjectScout",
  description: "TME internal product",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();

  if (!session?.user?.id) redirect("/api/auth/signout");

  return (
    <html lang="en">
      <SessionProvider session={session}>
        <body className={`${inter.className} bg-gray-100`}>
          <TME_navbar />
          {children}
        </body>
      </SessionProvider>
    </html>
  );
}
