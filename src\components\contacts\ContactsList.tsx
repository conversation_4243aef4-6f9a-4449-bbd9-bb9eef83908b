"use client";

import { useState } from "react";
import { ContactListItem, type Contact } from "./ContactListItem";
import { EmptyState } from "@/components/shared";

// Dummy contact data
const dummyContacts: Contact[] = [
  {
    id: 1,
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Tech Solutions Inc",
    role: "Project Manager",
    lastContact: "2024-01-15",
  },
  {
    id: 2,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Design Co",
    role: "Creative Director",
    lastContact: "2024-01-10",
  },
  {
    id: 3,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    company: "Startup XYZ",
    role: "CTO",
    lastContact: "2024-01-08",
  },
];

export const ContactsList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredContacts = dummyContacts.filter(
    (contact) =>
      contact.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.company.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddContact = () => {
    console.log("Add contact clicked");
  };

  const handleEditContact = (contact: Contact) => {
    console.log("Edit contact:", contact);
  };

  const handleContactAction = (contact: Contact) => {
    console.log("Contact action:", contact);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Contacts</h2>
          <p className="text-gray-600">Manage your professional contacts</p>
        </div>
        <button onClick={handleAddContact} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
          Add Contact
        </button>
      </div>

      {/* Search */}
      <div className="max-w-md">
        <input
          type="text"
          placeholder="Search contacts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Contacts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredContacts.map((contact) => (
          <ContactListItem key={contact.id} contact={contact} onEdit={handleEditContact} onContact={handleContactAction} />
        ))}
      </div>

      {filteredContacts.length === 0 && (
        <EmptyState
          icon={
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          }
          title="No contacts found"
          description="Try adjusting your search terms or add a new contact."
          action={{
            label: "Add Contact",
            onClick: handleAddContact,
          }}
        />
      )}
    </div>
  );
};
