import { Locator, type Page } from "@playwright/test";
import { ProjectsPagePom } from "./projects-page-pom";

export class MyProjectsPagePom extends ProjectsPagePom {
  constructor(page: Page) {
    super(page);
  }

  async goToMyProjectsPage() {
    await this.page.goto("/my-projects");
    await this.projectsListContainer.waitFor({ state: "visible" });
  }

  async getAllProjects(): Promise<Locator[]> {
    const count = await this.projectCount();
    const projects: Locator[] = [];
    for (let i = 0; i < count; i++) {
      projects.push(this.projectItem.nth(i));
    }
    return projects;
  }
}
