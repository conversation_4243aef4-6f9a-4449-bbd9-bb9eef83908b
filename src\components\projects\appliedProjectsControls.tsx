"use client";

import { useState } from "react";

import { useProjectContext } from "@/hooks/useProjectContext";
import { AddIcon, FilterIcon, AscendingIcon, DescendingIcon } from "../icons";

export const AppliedProjectsControls: React.FC = () => {
  const { openNewProjectForm, handleSearch, handleSort } = useProjectContext();
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  const toggleDropdown = () => {
    setDropdownOpen(!isDropdownOpen);
  };

  return (
    <nav className="bg-gray-100 px-4 py-2 block sticky z-10 top-[0] shadow-md">
      <div className="flex items-center gap-x-6 justify-between max-w-[900px] mx-auto">
        {/* Search field */}
        <div className="w-full">
          <input
            type="text"
            placeholder="Search..."
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            onChange={handleSearch}
            data-testid="searchInput"
          />
        </div>

        <div className="flex items-center gap-2">
          {/* Filter dropdown */}
          <div className="relative h-10">
            {/* Sort Button */}
            <button
              className="bg-gray-100 rounded hover:bg-gray-200"
              onClick={toggleDropdown}
              onBlur={() => setDropdownOpen(false)}
              data-testid="sortIcon"
            >
              <FilterIcon />
            </button>

            {/* Dropdown */}
            {isDropdownOpen && (
              <div
                className="absolute right-0 w-max mt-2 bg-white border rounded-lg shadow-lg z-20"
                onMouseDown={(e) => e.preventDefault()}
                data-testid="sortDropdown"
              >
                <ul className="space-y-2 p-4">
                  {[
                    { key: "id", label: "ID" },
                    { key: "projectTitle", label: "Project Title" },
                    { key: "contactFirstName", label: "Contact Name" },
                    { key: "agency", label: "Agency" },
                    { key: "createdForUser", label: "Created for" },
                    { key: "createdByUser", label: "Created by" },
                    { key: "rateOnsite", label: "Rate" },
                    {
                      key: "dateOfApplication",
                      label: "Date of Application",
                    },
                  ].map((option) => (
                    <li key={option.key} className="flex items-center justify-between">
                      <span className="mr-1">{option.label}</span>

                      <div className="flex gap-1">
                        <button onClick={() => handleSort(option.key, "asc")} className="hover:bg-gray-200" data-testid={`sort-asc-${option.key}`}>
                          <AscendingIcon />
                        </button>
                        <button onClick={() => handleSort(option.key, "desc")} className="hover:bg-gray-200" data-testid={`sort-desc-${option.key}`}>
                          <DescendingIcon />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="h-10 bg-gray-100 rounded hover:bg-gray-200 cursor-pointer">
            <AddIcon onNewItem={openNewProjectForm} />
          </div>
        </div>
      </div>
    </nav>
  );
};
