{"name": "tme-project_scout", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "test-e2e-ci": "playwright test", "prisma:push": "prisma db push", "prisma:seed": "prisma db seed", "prepare": "husky"}, "dependencies": {"@prisma/client": "6.2.1", "@radix-ui/react-tabs": "1.1.13", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "lucide-react": "0.542.0", "next": "14.2.12", "next-auth": "5.0.0-beta.25", "pg": "8.13.0", "react": "^18", "react-dom": "^18", "react-hook-form": "7.56.1", "swr": "2.2.5", "tailwind-merge": "3.3.1"}, "devDependencies": {"@faker-js/faker": "9.1.0", "@playwright/test": "1.48.1", "@tailwindcss/postcss": "4.1.12", "@types/node": "20.16.9", "@types/pg": "8.11.10", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "8.25.0", "@typescript-eslint/parser": "8.25.0", "autoprefixer": "10.4.20", "dotenv": "16.4.5", "eslint": "^8", "eslint-config-next": "14.2.7", "husky": "9.1.7", "postcss": "8.5.6", "prisma": "6.2.1", "tailwindcss": "4.1.12", "tw-animate-css": "1.3.7", "typescript": "^5"}, "prisma": {"seed": "npx tsx prisma/seed.ts"}}