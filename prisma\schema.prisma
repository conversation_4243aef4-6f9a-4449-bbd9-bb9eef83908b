// docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider          = "sqlserver"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model Project {
  id                 Int       @id @default(autoincrement())
  projectTitle       String
  jobReference       String?
  rateOnsite         String?
  rateOffsite        String?
  dateOfProjectStart DateTime?
  utilization        String?
  role               String?
  location           String?
  remote             String?
  agency             String?
  contactFirstName   String?
  contactLastName    String?
  email              String?
  number             String?
  platform           String?
  dateOfApplication  DateTime?
  projectLink        String?
  comment            String?
  status             String?   @default("active")
  //
  isDeleted          Boolean   @default(false)
  createdAt          DateTime  @default(now())
  lastUpdated        DateTime  @updatedAt
  //
  createdForUser     User      @relation(fields: [createdForUserId], references: [id], name: "projectCreatedForUser")
  createdForUserId   String
  createdByUser      User      @relation(fields: [createdByUserId], references: [id], name: "projectCreatedByUser", onDelete: NoAction, onUpdate: NoAction)
  createdByUserId    String
}

model User {
  id                    String    @id @default(cuid())
  name                  String?
  email                 String    @unique
  image                 String?   @db.NVarChar(4000)
  //
  isDeleted             Boolean   @default(false)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  //
  projectCreatedForUser Project[] @relation("projectCreatedForUser")
  projectCreatedByUser  Project[] @relation("projectCreatedByUser")
}

model Company {
  id          String    @id @default(cuid())
  name        String
  location    String?
  website     String?
  description String?
  //
  isDeleted   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  //
  Contact     Contact[]
}

model Contact {
  id          String    @id @default(cuid())
  firstName   String
  lastName    String
  email       String?
  phone       String?
  role        String?
  lastContact DateTime?
  //
  isDeleted   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  //
  company     Company?  @relation(fields: [companyId], references: [id])
  companyId   String?
}
