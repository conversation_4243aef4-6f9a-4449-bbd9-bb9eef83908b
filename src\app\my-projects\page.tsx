"use client";

import { useState } from "react";

import { ProjectList, AppliedProjectsControls } from "@/components/projects";
import { NotificationToast } from "@/components/shared";
import { ProjectProvider } from "@/contexts/projectsContext";

export default function MyProjects() {
  const [toastMessage, setToastMessage] = useState("");

  const showToast = (message: string) => {
    setToastMessage(message);
  };

  const closeToast = () => setToastMessage("");

  return (
    <ProjectProvider onlyOwnProjects={true}>
      <div className="relative">
        <AppliedProjectsControls />
        <ProjectList showToast={showToast} />
        <NotificationToast message={toastMessage} visible={!!toastMessage} onClose={closeToast} />
      </div>
    </ProjectProvider>
  );
}
