import Link from "next/link";
import { TrashIcon, EditIcon, DuplicateIcon } from "@/components/icons";
import { type ProjectIdType } from "@/types";

export const ProjectId: React.FC<{
  projectId: ProjectIdType;
  status: string;
}> = ({ projectId, status }) => {
  const statusColor = {
    active: "bg-green-600 text-gray-200",
    rejected: "bg-orange-600 text-gray-200",
    closed: "bg-red-600 text-gray-200",
  }[status];

  return (
    <p className={`px-2 ${statusColor} rounded cursor-default h-fit text-xs sm:text-sm`}>
      #<span data-testid="projectId">{projectId}</span>
      <span className="hidden" data-testid="projectStatus">
        {status}
      </span>
    </p>
  );
};

export const ProjectTitle: React.FC<{
  projectLink: string | null;
  projectTitle: string;
  refId?: string | null;
}> = ({ projectLink, projectTitle, refId }) => {
  let safeLink;

  if (!projectLink) safeLink = "";
  else if (projectLink.startsWith("http")) safeLink = projectLink;
  else safeLink = `https://${projectLink}`;

  return (
    <span className="flex items-start gap-1 flex-wrap min-w-0 flex-1">
      {projectLink ? (
        <Link
          href={safeLink}
          target="_blank"
          rel="noopener noreferrer"
          className="block w-full min-w-0 text-blue-600 break-all sm:break-words"
          data-testid="projectLink"
        >
          <span className="block text-xl sm:text-2xl font-semibold underline break-all sm:break-words leading-tight" data-testid="projectTitle">
            {projectTitle}
          </span>
          <span className="text-md font-medium text-gray-700 ml-0 sm:ml-1 block sm:inline" data-testid="jobReference">
            {refId ? `#${refId}` : ""}
          </span>
        </Link>
      ) : (
        <span className="block w-full min-w-0 break-all sm:break-words max-w-full">
          <span className="block text-blue-600 text-xl sm:text-2xl font-semibold break-all sm:break-words leading-tight" data-testid="projectTitle">
            {projectTitle}
          </span>
          <span className="text-md font-medium text-gray-700 ml-0 sm:ml-1 block sm:inline" data-testid="jobReference">
            {refId ? `#${refId}` : ""}
          </span>
        </span>
      )}
    </span>
  );
};

export const InfoItem: React.FC<{ label: string; value: string | null; testId: string }> = ({ label, value, testId }) => {
  return (
    <div>
      <p className="text-sm text-gray-600">{label}</p>
      <p className="text-md font-medium text-gray-900 leading-snug break-words">{!!value ? <span data-testid={testId}>{value}</span> : "---"}</p>
    </div>
  );
};

export const ActionGroup: React.FC<{
  projectId: ProjectIdType;
  startEditing: () => void;
  onDuplicate: () => void;
  onDelete: (projectId: ProjectIdType) => void;
  lastUpdated: Date | null;
}> = ({ projectId, startEditing, onDuplicate, onDelete, lastUpdated }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:justify-between items-start sm:items-end gap-2 mt-4">
      <p
        className="
          text-sm
          text-gray-600
          font-medium
          leading-snug
        "
      >
        Last Updated: {formatLastUpdated(lastUpdated!!)}
      </p>

      <div className="flex gap-4">
        <div className="relative group inline-block">
          <EditIcon onEdit={startEditing} />
          <span className="absolute z-50 left-1/2 -translate-x-1/2 mt-2 hidden group-hover:block w-max px-2 py-1 text-sm text-white bg-gray-600 rounded">
            Edit Project
          </span>
        </div>
        <div className="relative group inline-block">
          <DuplicateIcon onDuplicate={onDuplicate} />
          <span className="absolute z-50 left-1/2 -translate-x-1/2 mt-2 hidden group-hover:block w-max px-2 py-1 text-sm text-white bg-gray-600 rounded">
            Duplicate Project
          </span>
        </div>

        <div className="relative group inline-block">
          <TrashIcon onDelete={() => onDelete(projectId)} />
          <span className="absolute z-50 left-1/2 -translate-x-1/2 mt-2 hidden group-hover:block w-max px-2 py-1 text-sm text-white bg-gray-600 rounded">
            Delete Project
          </span>
        </div>
      </div>
    </div>
  );
};

export const DetailsContainer: React.FC<{ children: React.ReactNode; testId: string }> = ({ children, testId }) => {
  return (
    <div
      style={{
        boxShadow: "1px 1px 1px 0px rgba(0,0,0,0.3), inset 1px 1px 2px 0px rgba(255, 255, 255)",
      }}
      className="flex flex-col flex-1 min-w-[250px] gap-3 bg-gray-100 p-4 pt-0 rounded-lg"
      data-testid={testId}
    >
      {children}
    </div>
  );
};

const formatLastUpdated = (dateString: Date): string => {
  const date = new Date(dateString);

  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = String(date.getFullYear());
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${day}-${month}-${year} ${hours}:${minutes}`;
};
