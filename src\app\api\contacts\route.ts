import { NextResponse } from "next/server";

import { prisma } from "@@/prisma";
import { auth } from "@/auth";

export const GET = auth(async function GET() {
  try {
    const contacts = await prisma.contact.findMany({
      where: { isDeleted: false },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(contacts, { status: 200 });
  } catch (error) {
    return NextResponse.json({
      message: `Error getting contacts: ${error}`,
      status: 500,
    });
  }
});

export const POST = auth(async function POST(req) {
  try {
    const body = await req.json();

    // Validate required fields
    if (!body.firstName || !body.lastName) {
      return NextResponse.json(
        { error: "First name and last name are required" },
        { status: 400 }
      );
    }

    const newContact = await prisma.contact.create({
      data: {
        name: body.name,
        email: body.email || null,
        phone: body.phone || null,
        role: body.role || null,
        comments: body.comments || null,
        companyId: body.companyId || null,
      },
    });

    return NextResponse.json(newContact, { status: 201 });
  } catch (error) {
    return NextResponse.json({
      message: `Error creating contact: ${error}`,
      status: 500,
    });
  }
});
