"use client";

import { useState, useEffect } from "react";

import { NotificationToast } from "@/components/shared";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { ContactsList } from "@/components/contacts";
import { CompaniesList } from "@/components/companies";
import { ContactsIcon, CompanyIcon } from "@/components/icons";

export default function ContactsCompanies() {
  const [toastMessage, setToastMessage] = useState("");

  // Get the tab from URL hash or default to "contacts"
  const [activeTab, setActiveTab] = useState("contacts");

  useEffect(() => {
    // Check for hash in URL on component mount
    const hash = window.location.hash.replace("#", "");
    if (hash === "contacts" || hash === "companies") {
      setActiveTab(hash);
    }
  }, []);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL hash without triggering a page reload
    window.history.replaceState(null, "", `#${value}`);
  };

  const closeToast = () => setToastMessage("");

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Contacts & Companies</h1>
            <p className="text-lg text-gray-600">Manage your contacts and company relationships</p>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="contacts" className="flex items-center gap-2">
                  <ContactsIcon />
                  Contacts
                </TabsTrigger>
                <TabsTrigger value="companies" className="flex items-center gap-2">
                  <CompanyIcon />
                  Companies
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="contacts" className="mt-0">
              <ContactsList />
            </TabsContent>

            <TabsContent value="companies" className="mt-0">
              <CompaniesList />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <NotificationToast message={toastMessage} visible={!!toastMessage} onClose={closeToast} />
    </div>
  );
}
