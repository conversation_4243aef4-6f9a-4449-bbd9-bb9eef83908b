import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@@/prisma";
import { auth } from "@/auth";

export const GET = auth(async function GET(
  _: NextRequest,
  ctx: { params?: { id?: string } }
) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Contact ID is required" }, { status: 400 });
    }

    const contact = await prisma.contact.findFirst({
      where: { 
        id,
        isDeleted: false 
      },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    return NextResponse.json(contact, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error getting contact: ${error}` }, { status: 500 });
  }
});

export const DELETE = auth(async function DELETE(
  _: NextRequest,
  ctx: { params?: { id?: string } }
) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Contact ID is required" }, { status: 400 });
    }

    const deletedContact = await prisma.contact.update({
      where: { id },
      data: { isDeleted: true },
    });

    return NextResponse.json(deletedContact, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error deleting contact: ${error}` }, { status: 500 });
  }
});

export const PUT = auth(async function PUT(req: NextRequest, ctx: { params?: { id?: string } }) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Contact ID is required" }, { status: 400 });
    }

    const body = await req.json();
    const { updates } = body;

    // Validate that required fields are not empty if being updated
    if ((updates.firstName !== undefined && !updates.firstName) || 
        (updates.lastName !== undefined && !updates.lastName)) {
      return NextResponse.json(
        { error: "First name and last name cannot be empty" },
        { status: 400 }
      );
    }

    // Handle date conversion for lastContact if provided
    const updateData = { ...updates };
    if (updateData.lastContact) {
      updateData.lastContact = new Date(updateData.lastContact);
    }

    const updatedContact = await prisma.contact.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(updatedContact, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error updating contact: ${error}` }, { status: 500 });
  }
});
