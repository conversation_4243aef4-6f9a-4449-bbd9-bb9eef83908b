import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@@/prisma";
import { auth } from "@/auth";

export const GET = auth(async function GET(
  _: NextRequest,
  ctx: { params?: { id?: string } }
) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 });
    }

    const company = await prisma.company.findFirst({
      where: { 
        id,
        isDeleted: false 
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    return NextResponse.json(company, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error getting company: ${error}` }, { status: 500 });
  }
});

export const DELETE = auth(async function DELETE(
  _: NextRequest,
  ctx: { params?: { id?: string } }
) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 });
    }

    const deletedCompany = await prisma.company.update({
      where: { id },
      data: { isDeleted: true },
    });

    return NextResponse.json(deletedCompany, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error deleting company: ${error}` }, { status: 500 });
  }
});

export const PUT = auth(async function PUT(req: NextRequest, ctx: { params?: { id?: string } }) {
  try {
    const id = ctx.params?.id;

    if (!id) {
      return NextResponse.json({ error: "Company ID is required" }, { status: 400 });
    }

    const body = await req.json();
    const { updates } = body;

    // Validate that name is not empty if being updated
    if (updates.name !== undefined && !updates.name) {
      return NextResponse.json(
        { error: "Company name cannot be empty" },
        { status: 400 }
      );
    }

    const updatedCompany = await prisma.company.update({
      where: { id },
      data: {
        ...updates,
      },
    });

    return NextResponse.json(updatedCompany, { status: 200 });
  } catch (error) {
    return NextResponse.json({ message: `Error updating company: ${error}` }, { status: 500 });
  }
});
